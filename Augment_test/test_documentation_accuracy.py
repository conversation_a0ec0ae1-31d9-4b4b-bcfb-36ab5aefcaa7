#!/usr/bin/env python3
"""
Test script to verify the accuracy of the updated README.md documentation.

This script checks that the documented files and modules exist and can be imported
when run from the project root directory.
"""

import os
import sys
from pathlib import Path

def test_project_structure():
    """Test that documented project structure matches actual files."""
    print("Testing project structure...")
    
    # Key files and directories mentioned in README
    expected_paths = [
        "app/llm/bedrock.py",
        "app/llm/extraction.py", 
        "app/llm/classification.py",
        "app/llm/factory.py",
        "app/utils/textract.py",
        "app/utils/langfuse_util.py",
        "app/core/configuration.py",
        "app/services/document_extraction_processor.py",
        "app/services/document_classification_processor",
        "app/prompts/invoice_extraction.py",
        "app/prompts/classification.py",
        "docker-compose.yml",
        "env.example"
    ]
    
    missing_files = []
    for path in expected_paths:
        if not Path(path).exists():
            missing_files.append(path)
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    else:
        print("✅ All documented files exist")
        return True

def test_configuration_variables():
    """Test that documented configuration variables exist in settings."""
    print("\nTesting configuration variables...")
    
    try:
        # Add project root to path for imports
        project_root = Path(__file__).parent.parent
        sys.path.insert(0, str(project_root))
        
        from app.core.configuration import settings
        
        # Key configuration variables mentioned in README
        required_vars = [
            'AWS_REGION_EXTRACTION',
            'MODEL_ID_EXTRACTION', 
            'AWS_REGION_CLASSIFICATION',
            'MODEL_ID_CLASSIFICATION',
            'TEXTRACT_MAX_WORKERS',
            'LANGFUSE_ENABLED',
            'S3_BUCKET_NAME'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not hasattr(settings, var):
                missing_vars.append(var)
        
        if missing_vars:
            print(f"❌ Missing configuration variables: {missing_vars}")
            return False
        else:
            print("✅ All documented configuration variables exist")
            return True
            
    except ImportError as e:
        print(f"❌ Could not import settings: {e}")
        return False

def test_llm_modules():
    """Test that LLM modules can be imported and have expected functions."""
    print("\nTesting LLM modules...")
    
    try:
        # Add project root to path for imports
        project_root = Path(__file__).parent.parent
        sys.path.insert(0, str(project_root))
        
        # Test extraction module
        from app.llm import extraction
        if not hasattr(extraction, 'main'):
            print("❌ extraction.py missing main function")
            return False
            
        # Test classification module  
        from app.llm import classification
        if not hasattr(classification, 'main'):
            print("❌ classification.py missing main function")
            return False
            
        # Test bedrock module
        from app.llm import bedrock
        if not hasattr(bedrock, 'BedrockProcessor'):
            print("❌ bedrock.py missing BedrockProcessor class")
            return False
            
        print("✅ All LLM modules imported successfully")
        return True
        
    except ImportError as e:
        print(f"❌ Could not import LLM modules: {e}")
        return False

def main():
    """Run all documentation accuracy tests."""
    print("🔍 Testing README.md documentation accuracy...\n")
    
    # Change to project root directory
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    tests = [
        test_project_structure,
        test_configuration_variables,
        test_llm_modules
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    print(f"\n📊 Test Results: {sum(results)}/{len(results)} passed")
    
    if all(results):
        print("🎉 All documentation tests passed! README.md is accurate.")
        return True
    else:
        print("❌ Some documentation tests failed. Please review README.md.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
