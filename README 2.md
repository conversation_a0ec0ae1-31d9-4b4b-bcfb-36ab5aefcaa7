# Project Overview: Document Data Extraction & Classification

## 1. High-Level Goal

This project provides a robust, asynchronous pipeline for extracting structured data from documents as well as classifying documents. It leverages cloud services and various Large Language Models (LLMs) to process files uploaded to an S3 bucket, storing the extracted data in a searchable format and storing the classified documents back in S3.

## 2. Core Technologies

- **Backend Framework**: FastAPI
- **Cloud Services**:
  - AWS S3 (for document storage and output)
  - AWS SQS (for asynchronous message queuing)
  - AWS Textract (for document text extraction and OCR)
  - AWS Bedrock (for AI-powered data extraction and classification)
- **Database / Search**: OpenSearch (for data indexing and vector search)
- **LLM Integration**:
  - **Primary**: AWS Bedrock with models like `amazon.nova-pro-v1:0` (extraction) and `openai.gpt-oss-20b-1:0` (classification)
  - **Monitoring**: Langfuse for LLM observability and tracking
- **Async Processing**: `asyncio`, `aioboto3` for non-blocking I/O operations
- **Containerization**: Docker, Docker Compose

## 3. Architecture Overview

The application follows an event-driven, asynchronous architecture with two main processing pipelines:

### Document Extraction Pipeline
1.  **Upload**: Documents (PDF, JPG, JPEG, PNG, TIF, TIFF) are uploaded to designated S3 bucket paths
2.  **Trigger**: S3 upload events trigger notifications to the extraction SQS queue
3.  **Text Extraction**: The extraction worker uses **AWS Textract** to extract text and layout information from documents
4.  **AI Processing**: Extracted text is sent to **AWS Bedrock** (using models like `amazon.nova-pro-v1:0`) for structured data extraction
5.  **Output**: Results are saved as JSON files in S3 output folders

### Document Classification Pipeline
1.  **Upload**: Documents are uploaded to classification-specific S3 paths
2.  **Trigger**: S3 events trigger notifications to the classification SQS queue
3.  **Text Extraction**: AWS Textract processes documents (with multi-page PDF support)
4.  **AI Classification**: **AWS Bedrock** (using models like `openai.gpt-oss-20b-1:0`) classifies document types
5.  **Document Splitting**: Based on classification, PDFs are split into separate documents by type
6.  **Output**: Classified and split documents are saved back to S3

### Key Features
- **Parallel Processing**: Multi-page documents are processed concurrently
- **Monitoring**: Langfuse integration for LLM call tracking and observability
- **Error Handling**: Comprehensive error handling with detailed logging
- **Scalability**: Asynchronous processing with configurable worker concurrency

## 4. Project Structure

- **/app**: The core FastAPI application code.
  - **/api**: API endpoints, request/response models, and dependencies
  - **/core**: Core application logic and configuration
    - `configuration.py`: Environment settings and AWS/Bedrock configurations
    - `opensearch.py`: OpenSearch connection management
    - `logger.py`: Logging configuration
  - **/llm**: LLM integration and processing modules
    - `bedrock.py`: AWS Bedrock client and helper methods
    - `extraction.py`: Document data extraction pipeline using Bedrock
    - `classification.py`: Document classification pipeline using Bedrock
    - `factory.py`: LLM model factory and configuration
    - `openai.py`, `azure_openai.py`: Alternative LLM providers
  - **/services**: Asynchronous worker services for document processing
    - `document_extraction_processor.py`: SQS worker for extraction pipeline
    - `document_classification_processor/`: SQS worker for classification pipeline
    - `s3_service.py`: S3 client and operations
  - **/utils**: Utility modules
    - `textract.py`: AWS Textract integration for document text extraction
    - `langfuse_util.py`: Langfuse monitoring and observability
    - `text_utils.py`: Text processing utilities
    - `pdf_utils.py`: PDF manipulation utilities
  - **/prompts**: LLM prompt templates
    - `invoice_extraction.py`: Extraction prompts
    - `classification.py`: Classification prompts
  - **/schemas**: Pydantic models for data validation
  - **/crud**: Data access operations for OpenSearch
  - **/vectorstore**: Vector database operations and embeddings
  - **/parsers**: Document parsing and chunking utilities
  - **/middleware**: FastAPI middleware components
  - **/exceptions**: Custom exception classes
  - **`main.py`**: FastAPI application entry point
- **/scripts**: Utility scripts for setup and maintenance
- **`docker-compose.yml`**: Multi-service Docker configuration (API, OpenSearch, Redis, PostgreSQL)
- **`Dockerfile`**: Python application container definition
- **`env.example`**: Environment variables template

## 5. Configuration

The application requires several environment variables for proper operation. Copy `env.example` to `.env` and configure the following:

### AWS Configuration
```bash
# Basic AWS credentials
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=us-east-1

# S3 Configuration
S3_BUCKET_NAME=your-document-bucket
EXTRACTION_FOLDER_INPUT_PREFIX=input/extraction-input
EXTRACTION_FOLDER_OUTPUT_PREFIX=output/extraction-output

# SQS Configuration
SQS_EXTRACTION_QUEUE_URL=https://sqs.us-east-1.amazonaws.com/account/extraction-queue
SQS_CLASSIFICATION_QUEUE_URL=https://sqs.us-east-1.amazonaws.com/account/classification-queue
```

### AWS Bedrock Configuration

#### For Document Extraction
```bash
AWS_REGION_EXTRACTION=us-east-1
MODEL_ID_EXTRACTION=amazon.nova-pro-v1:0
TEMPERATURE_EXTRACTION=0.2
TOP_P_EXTRACTION=0.5
MAX_TOKENS_EXTRACTION=500
S3_SAMPLE_FILE_EXTRACTION=s3://your-bucket/sample/invoice.pdf
```

#### For Document Classification
```bash
AWS_REGION_CLASSIFICATION=us-west-2
MODEL_ID_CLASSIFICATION=openai.gpt-oss-20b-1:0
TEMPERATURE_CLASSIFICATION=0.7
MAX_TOKENS_CLASSIFICATION=5000
REASONING_EFFORT_CLASSIFICATION=medium
S3_SAMPLE_FILE_CLASSIFICATION=s3://your-bucket/sample/document.pdf
```

### AWS Textract Configuration
```bash
TEXTRACT_MAX_WORKERS=6
TEXTRACT_POLL_INTERVAL=5
TEXTRACT_MAX_WAIT_TIME=300
TEXTRACT_MAX_FILE_SIZE_MB=10
TEXTRACT_TEMP_PREFIX=temp-document-processing
```

### Langfuse Monitoring (Optional)
```bash
LANGFUSE_PUBLIC_KEY=pk-lf-your-public-key
LANGFUSE_SECRET_KEY=sk-lf-your-secret-key
LANGFUSE_HOST=https://cloud.langfuse.com
LANGFUSE_ENABLED=true
```

### OpenSearch Configuration
```bash
OPENSEARCH_URL=http://localhost:9200
OPENSEARCH_USERNAME=admin
OPENSEARCH_PASSWORD=admin
OPENSEARCH_VECTOR_INDEX=documents
```

## 6. Getting Started

### Prerequisites
- Docker and Docker Compose installed
- AWS account with appropriate permissions for S3, SQS, Textract, and Bedrock
- Configured AWS credentials

### Setup Steps

1. **Clone and Configure Environment**
   ```bash
   git clone <repository-url>
   cd document-data-extraction
   cp env.example .env
   # Edit .env with your AWS credentials and configuration
   ```

2. **Start Infrastructure Services**
   ```bash
   docker-compose up -d opensearch redis postgres
   ```

3. **Run FastAPI Application**
   ```bash
   docker-compose up -d backend
   # API will be available at http://localhost:8000
   ```

4. **Start Document Processing Workers**

   For **extraction processing**:
   ```bash
   python app/services/document_extraction_processor.py
   ```

   For **classification processing**:
   ```bash
   python app/services/document_classification_processor
   ```

### Testing the Pipelines

#### Test Document Extraction
```bash
# Run extraction on sample file
cd app/llm
python extraction.py
```

#### Test Document Classification
```bash
# Run classification on sample file
cd app/llm
python classification.py
```

### Production Deployment
- Configure SQS queues to trigger on S3 upload events
- Deploy workers as separate services/containers
- Set up appropriate IAM roles and policies for AWS services
- Configure monitoring and alerting through Langfuse and CloudWatch

## 7. LLM Processing Modules

### Document Extraction (`app/llm/extraction.py`)
- **Purpose**: Extract structured data from invoices and documents
- **Model**: `amazon.nova-pro-v1:0` (configurable via `MODEL_ID_EXTRACTION`)
- **Input**: S3 URI pointing to document
- **Process**:
  1. Uses AWS Textract for text extraction
  2. Sends structured text to Bedrock for data extraction
  3. Returns JSON with extracted fields (vendor, invoice number, amounts, etc.)
- **Monitoring**: Integrated with Langfuse for tracking
- **Usage**: `python app/llm/extraction.py` or `await main(s3_uri)`

### Document Classification (`app/llm/classification.py`)
- **Purpose**: Classify logistics documents into predefined categories
- **Model**: `openai.gpt-oss-20b-1:0` (configurable via `MODEL_ID_CLASSIFICATION`)
- **Input**: S3 URI pointing to document (supports multi-page PDFs)
- **Process**:
  1. Uses AWS Textract with parallel page processing
  2. Combines page text with `<page1></page1>` tags
  3. Uses Bedrock with structured tool calling for classification
  4. Returns document types with page numbers
- **Features**: Parallel processing, structured output, comprehensive error handling
- **Usage**: `python app/llm/classification.py` or `await main(file_path)`

### Bedrock Integration (`app/llm/bedrock.py`)
- **Purpose**: Centralized AWS Bedrock client and helper methods
- **Features**:
  - Converse API integration with tool calling support
  - Text and tool response extraction helpers
  - Comprehensive error handling and logging
  - Configurable model parameters (temperature, top_p, max_tokens)

## 8. Architecture Diagram

You can render the following Mermaid code in a compatible editor (like GitHub's Markdown viewer or a free online tool) to visualize the project architecture.

```mermaid
graph TD
    %% User Interaction
    subgraph "User Interaction"
        User["User"]
    end

    %% AWS Cloud
    subgraph "AWS Cloud"
        S3Input["S3 Bucket (Input)"]
        S3Input --> S3Extraction["extraction-input/"]
        S3Input --> S3Classification["classification-input/"]

        SQSExtraction["SQS Queue (Extraction)"]
        SQSClassification["SQS Queue (Classification)"]

        Textract["AWS Textract"]
        Bedrock["AWS Bedrock"]

        S3OutputExtraction["S3 Bucket (Extraction Output)"]
        S3OutputClassification["S3 Bucket (Classification Output)"]
    end

    %% Application Services
    subgraph "Application Services (Docker)"
        FastAPI["FastAPI API"]
        ExtractionWorker["Extraction Worker"]
        ClassificationWorker["Classification Worker"]
    end

    %% Monitoring
    subgraph "Monitoring & Observability"
        Langfuse["Langfuse (LLM Monitoring)"]
        OpenSearch["OpenSearch (Data Indexing)"]
    end

    %% Flow
    User -- "Uploads Document" --> FastAPI
    FastAPI -- "Stores File" --> S3Input

    %% Extraction Flow
    S3Extraction -- "Triggers Event" --> SQSExtraction
    SQSExtraction -- "Consumed by" --> ExtractionWorker
    ExtractionWorker -- "Fetches Document" --> S3Extraction
    ExtractionWorker -- "Text Extraction" --> Textract
    Textract -- "Structured Text" --> ExtractionWorker
    ExtractionWorker -- "Data Extraction" --> Bedrock
    Bedrock -- "Structured JSON" --> ExtractionWorker
    ExtractionWorker -- "Stores JSON Output" --> S3OutputExtraction
    ExtractionWorker -- "Monitoring" --> Langfuse

    %% Classification Flow
    S3Classification -- "Triggers Event" --> SQSClassification
    SQSClassification -- "Consumed by" --> ClassificationWorker
    ClassificationWorker -- "Fetches Document" --> S3Classification
    ClassificationWorker -- "Text Extraction" --> Textract
    Textract -- "Page Text" --> ClassificationWorker
    ClassificationWorker -- "Classification" --> Bedrock
    Bedrock -- "Document Types" --> ClassificationWorker
    ClassificationWorker -- "Splits & Stores PDFs" --> S3OutputClassification
    ClassificationWorker -- "Monitoring" --> Langfuse

    %% Data Flow
    S3OutputExtraction -- "Optional Indexing" --> OpenSearch
    FastAPI -- "Query Interface" --> OpenSearch

    %% Styling
    classDef aws fill:#FF9900,stroke:#333,stroke-width:2px,color:#fff;
    class S3Input,S3Extraction,S3Classification,SQSExtraction,SQSClassification,S3OutputExtraction,S3OutputClassification,Textract,Bedrock aws;

    classDef app fill:#264DE4,stroke:#333,stroke-width:2px,color:#fff;
    class FastAPI,ExtractionWorker,ClassificationWorker app;

    classDef monitoring fill:#4CAF50,stroke:#333,stroke-width:2px,color:#fff;
    class Langfuse,OpenSearch monitoring;


```